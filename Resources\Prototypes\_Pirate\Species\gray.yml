- type: species
  id: Gray
  name: <PERSON><PERSON><PERSON><PERSON>
  roundStart: false
  prototype: MobGray
  sprites: MobGraySprites
  defaultSkinTone: "#b8b8b8"
  markingLimits: MobGrayMarkingLimits
  dollPrototype: MobGrayDummy
  skinColoration: TintedHues
  maleFirstNames: names_gray_first
  femaleFirstNames: names_gray_first
  lastNames: names_gray_last

# The lack of a layer means that
# this person cannot have round-start anything
# applied to that layer. It has to instead
# be defined as a 'custom base layer'
# in either the mob's starting marking prototype,
# or it has to be added in C#.
- type: speciesBaseSprites
  id: MobGraySprites
  sprites:
    Head: MobGrayHead
    Chest: MobGrayTorso
    Eyes: MobGrayEyes
    LArm: MobGrayLArm
    RArm: MobGrayRArm
    LHand: MobGrayLHand
    RHand: MobGrayRHand
    LLeg: MobGrayLLeg
    RLeg: MobGrayRLeg
    LFoot: MobGrayLFoot
    RFoot: MobGrayRFoot

- type: markingPoints
  id: MobGrayMarkingLimits
  onlyWhitelisted: true
  points:
  # PIRATE START
    Face:
      points: 3
      required: false
  # PIRATE END
    Head:
      points: 1
      required: true
      defaultMarkings: [ GrayCuteEyes ]
    HeadTop:
      points: 0
      required: false
    HeadSide:
      points: 0
      required: false
    Chest:
      points: 0
      required: false
    RightLeg:
      points: 2
      required: false
    RightFoot:
      points: 2
      required: false
    LeftLeg:
      points: 2
      required: false
    LeftFoot:
      points: 2
      required: false
    RightArm:
      points: 2
      required: false
    RightHand:
      points: 2
      required: false
    LeftArm:
      points: 2
      required: false
    LeftHand:
      points: 2
      required: false
    Overlay:
      points: 0
      required: false

- type: humanoidBaseSprite
  id: MobGrayEyes
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobGrayHead
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobGrayHeadMale
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobGrayHeadFemale
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobGrayTorso
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobGrayTorsoMale
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobGrayTorsoFemale
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobGrayLLeg
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobGrayLArm
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobGrayLHand
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobGrayLFoot
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobGrayRLeg
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobGrayRArm
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobGrayRHand
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobGrayRFoot
  baseSprite:
    sprite: _Pirate/Mobs/Species/Gray/parts.rsi
    state: r_foot
