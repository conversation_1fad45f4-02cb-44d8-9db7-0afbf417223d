- type: entity
  save: false
  name: Urist McVulp
  parent: BaseMobSpeciesOrganic
  id: BaseMobVulpkanin
  abstract: true
  components:
  - type: HumanoidAppearance
    species: Vulpkanin
  - type: Hunger
    baseDecayRate: 0.02083333332 # 25% more than default
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Inventory # Allows vulps to wear properly shaped helmets
    templateId: vulpkanin #PIRATE
    speciesId: vulpkanin
  - type: Thirst
  - type: Icon
    sprite: DeltaV/Mobs/Species/Vulpkanin/parts.rsi
    state: full
  - type: Body
    prototype: Vulpkanin
    requiredLegs: 2
  - type: Speech
    speechSounds: Vulpkanin
    speechVerb: Vulpkanin
  - type: Sprite
    netsync: false
    noRot: true
    drawdepth: Mobs
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.Face" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
#     - shader: StencilClear
#      sprite: Mobs/Species/Human/parts.rsi
#      state: l_leg
      - map: [ "enum.HumanoidVisualLayers.Underwear" ]
      - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
      - map: [ "underpants" ]
      - map: [ "undershirt" ]
      - map: [ "jumpsuit" ]
      - map: [ "enum.HumanoidVisualLayers.LHand" ]
      - map: [ "enum.HumanoidVisualLayers.RHand" ]
      - map: [ "enum.HumanoidVisualLayers.LFoot" ]
      - map: [ "enum.HumanoidVisualLayers.RFoot" ]
      - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "id" ]
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "innerBelt" ]
      - map: [ "innerNeck" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "neck2" ] #PIRATE
      - map: [ "neck1" ] #PIRATE
      - map: [ "head2" ] #PIRATE
      - map: [ "head1" ] #PIRATE
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "DeltaV/Effects/creampie.rsi"
        state: "creampie_vulpkanin"
        visible: false
  - type: MeleeWeapon
    hidden: true
    soundHit:
      path: /Audio/Weapons/pierce.ogg
    angle: 30
    animation: WeaponArcClaw
    damage:
      types:
        Blunt: 2
        Slash: 3
  - type: Perishable
  - type: Damageable
    damageModifierSet: Vulpkanin
  - type: TemperatureProtection
    coefficient: 0.1
  - type: Vocal
    sounds:
      Male: MaleVulpkanin
      Female: FemaleVulpkanin
      Unsexed: MaleVulpkanin
  - type: DogVision
  - type: BarkAccent #PIRATE
  - type: ScentTracker
  - type: Flashable
    eyeDamageChance: 0.3
    eyeDamage: 1
    durationMultiplier: 1.5
  - type: Wagging
  - type: LanguageKnowledge
    speaks:
    - TauCetiBasic
    - Canilunzt
    understands:
    - TauCetiBasic
    - Canilunzt
  - type: ConsumeDelayModifier
    foodDelayMultiplier: 0.5
    drinkDelayMultiplier: 0.5
  - type: Flammable
    fireStackIncreaseMultiplier: 1.25
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - VulpEmotes

- type: entity
  save: false
  name: Vulpkanin Dummy
  parent: MobHumanDummy
  id: MobVulpkaninDummy
  categories: [ HideSpawnMenu ]
  description: A dummy vulpkanin meant to be used in character setup.
  components:
  - type: HumanoidAppearance
    species: Vulpkanin
    hideLayersOnEquip:
    - Snout
    - HeadTop
    - HeadSide
  - type: Inventory
    templateId: vulpkanin #PIRATE
    speciesId: vulpkanin
