# TODO: Add descriptions (many)
# TODO BODY: Part damage
- type: entity
  id: PartReptilian
  parent: [BaseItem, BasePart]
  name: "reptilian body part"
  abstract: true
  components:
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10

- type: entity
  id: TorsoReptilian
  name: "reptilian torso"
  parent: [PartReptilian, BaseTorso]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "torso_m"
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20

- type: entity
  id: HeadReptilian
  name: "reptilian head"
  parent: [PartReptilian, BaseHead]
  components:
  - type: Sprite
    # PIRATE START
    netsync: false
    sprite: _Pirate/Mobs/Customization/rept_head.rsi
    state: "head_base"
  - type: Icon
    sprite: _Pirate/Mobs/Customization/rept_head.rsi
    state: "head_base"
  - type: BodyPart  
    partType: Head
    organs:
      eyes:
        id: "eyes"
    vital: true
    # PIRATE END
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 5
      - ReagentId: Blood
        Quantity: 10
    # PIRATE START
  - type: Tag
    tags:
      - Head
    # PIRATE END

- type: entity
  id: LeftArmReptilian
  name: "left reptilian arm"
  parent: [PartReptilian, BaseLeftArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_arm"

- type: entity
  id: RightArmReptilian
  name: "right reptilian arm"
  parent: [PartReptilian, BaseRightArm]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_arm"

- type: entity
  id: LeftHandReptilian
  name: "left reptilian hand"
  parent: [PartReptilian, BaseLeftHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_hand"

- type: entity
  id: RightHandReptilian
  name: "right reptilian hand"
  parent: [PartReptilian, BaseRightHand]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_hand"

- type: entity
  id: LeftLegReptilian
  name: "left reptilian leg"
  parent: [PartReptilian, BaseLeftLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_leg"

- type: entity
  id: RightLegReptilian
  name: "right reptilian leg"
  parent: [PartReptilian, BaseRightLeg]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_leg"

- type: entity
  id: LeftFootReptilian
  name: "left reptilian foot"
  parent: [PartReptilian, BaseLeftFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "l_foot"

- type: entity
  id: RightFootReptilian
  name: "right reptilian foot"
  parent: [PartReptilian, BaseRightFoot]
  components:
  - type: Sprite
    sprite: Mobs/Species/Reptilian/parts.rsi
    state: "r_foot"
