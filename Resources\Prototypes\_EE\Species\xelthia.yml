- type: species
  id: Xelthia
  name: species-name-xelthia
  roundStart: true
  prototype: MobXelthia
  sprites: MobXelthiaSprites
  defaultSkinTone: "#56a2de"
  markingLimits: MobXelthiaMarkingLimits
  dollPrototype: MobXelthiaDummy
  skinColoration: Hues
  maleFirstNames: names_xelthia_first
  femaleFirstNames: names_xelthia_first
  lastNames: names_xelthia_last
  minHeight: 0.7
  defaultHeight: 0.95
  maxHeight: 1.25
  minWidth: 0.65
  defaultWidth: 0.95
  maxWidth: 1.3

- type: speciesBaseSprites
  id: MobXelthiaSprites
  sprites:
    Head: MobXelthiaHead
    Face: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Chest: MobXelthiaTorso
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Eyes: MobXelthiaEyes
    LArm: MobXelthiaLArm
    RArm: MobXelthiaRArm
    LHand: MobXelthiaLHand
    RHand: MobXelthiaRHand
    LLeg: MobXelthiaLLeg
    RLeg: MobXelthiaRLeg
    LFoot: MobXelthiaLFoot
    RFoot: MobXelthiaRFoot

- type: humanoidBaseSprite
  id: MobXelthiaEyes
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: eyes

- type: markingPoints
  id: MobXelthiaMarkingLimits
  onlyWhitelisted: true
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
  # PIRATE END
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 0
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ XelthiaTailNormal ]
    Snout:
      points: 1
      required: true
      defaultMarkings: [ XelthiaEarsNormal ]
    HeadTop:
      points: 6
      required: false
    HeadSide:
      points: 6
      required: false
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false
    Overlay:
      points: 1
      required: true
      defaultMarkings: [ XelthiaBackspikes ]
- type: humanoidBaseSprite
  id: MobXelthiaHead
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobXelthiaHeadMale
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobXelthiaHeadFemale
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobXelthiaTorso
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobXelthiaTorsoMale
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobXelthiaTorsoFemale
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobXelthiaLLeg
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobXelthiaLHand
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobXelthiaLArm
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobXelthiaLFoot
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobXelthiaRLeg
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobXelthiaRHand
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobXelthiaRArm
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobXelthiaRFoot
  baseSprite:
    sprite: _EE/Mobs/Species/Xelthia/parts.rsi
    state: r_foot
