- type: species
  id: Arachne
  name: species-name-arachne
  roundStart: true # I'll kill these issues somehow.
  prototype: MobArachne
  sprites: MobArachneSprites
  defaultSkinTone: "#c0967f"
  markingLimits: MobArachneMarkingLimits
  dollPrototype: MobArachneDummy
  skinColoration: HumanToned
  minAge: 60
  youngAge: 150
  oldAge: 400
  maxAge: 666

- type: markingPoints
  id: MobArachneMarkingLimits
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
  # PIRATE END
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    HeadSide:
      points: 3
      required: false
    Tail:
      points: 1
      required: false
    Chest:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false


- type: speciesBaseSprites
  id: MobArachneSprites
  sprites:
    Head: MobHumanHead
    Face: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Chest: MobHumanTorso
    Eyes: MobArachneEyes
    LArm: MobHumanLArm
    RArm: MobHumanRArm
    LHand: MobHumanLHand
    RHand: MobHumanRHand
    Undershirt: MobHumanoidAnyMarking

- type: humanoidBaseSprite
  id: MobArachneEyes
  baseSprite:
    sprite: Mobs/Species/eyes.rsi
    state: eyes
