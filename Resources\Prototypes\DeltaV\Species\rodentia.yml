- type: species
  id: Rodentia
  name: species-name-rodentia
  roundStart: true
  prototype: MobRodentia
  sprites: MobRodentiaSprites
  defaultSkinTone: "#747474"
  markingLimits: MobRodentiaMarkingLimits
  dollPrototype: MobRodentiaDummy
  skinColoration: Hues
  maleFirstNames: names_rodentia_male
  femaleFirstNames: names_rodentia_female
  lastNames: names_rodentia_last
  naming: LastFirst

- type: speciesBaseSprites
  id: MobRodentiaSprites
  sprites:
    Head: MobRodentiaHead
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Chest: MobRodentiaTorso
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Eyes: MobHumanoidEyes
    LArm: MobRodentiaLArm
    RArm: MobRodentiaRArm
    LHand: MobRodentiaLHand
    RHand: MobRodentiaRHand
    LLeg: MobRodentiaLLeg
    RLeg: MobRodentiaRLeg
    LFoot: MobRodentiaLFoot
    RFoot: MobRodentiaRFoot

- type: markingPoints
  id: MobRodentiaMarkingLimits
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
  # PIRATE END
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ RodentiaTailDefault ]

    RightLeg:
      points: 2
      required: false
    RightFoot:
      points: 2
      required: false
    LeftLeg:
      points: 2
      required: false
    LeftFoot:
      points: 2
      required: false

    RightArm:
      points: 2
      required: false
    RightHand:
      points: 3
      required: false
    LeftArm:
      points: 2
      required: false
    LeftHand:
      points: 3
      required: false

    Snout:
      points: 1
      required: false
    HeadTop:
      points: 1
      required: true
      defaultMarkings: [ RodentiaHeadTopEarDefault ]
    HeadSide:
      points: 2
      required: false
    Chest:
      points: 2
      required: false

- type: humanoidBaseSprite
  id: MobRodentiaHead
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobRodentiaHeadMale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobRodentiaHeadFemale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobRodentiaTorso
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobRodentiaTorsoMale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobRodentiaTorsoFemale
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobRodentiaLLeg
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobRodentiaLHand
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobRodentiaLArm
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobRodentiaLFoot
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobRodentiaRLeg
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobRodentiaRHand
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobRodentiaRArm
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobRodentiaRFoot
  baseSprite:
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: r_foot
