#  defaultSkinTone: "#486a1b"
- type: species
  id: Goblin
  name: species-name-goblin
  roundStart: true
  prototype: MobGoblin
  sprites: MobGoblinSprites
  markingLimits: MobGoblinMarkingLimits
  dollPrototype: MobGoblinDummy
  skinColoration: HumanToned #Possible values: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HumanToned
  maleFirstNames: names_goblin_male
  femaleFirstNames: names_goblin_female
  lastNames: names_goblin_last

- type: speciesBaseSprites
  id: MobGoblinSprites
  sprites:
    Hair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Head: MobGoblinHead
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Chest: MobGoblinTorso
    Eyes: MobGoblinEyes
    LArm: MobGoblinLArm
    RArm: MobGoblinRArm
    LHand: MobGoblinLHand
    RHand: MobGoblinRHand
    LLeg: MobGoblinLLeg
    RLeg: MobGoblinRLeg
    LFoot: MobGoblinLFoot
    RFoot: MobGoblinRFoot

- type: markingPoints
  id: MobGoblinMarkingLimits
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
  # PIRATE END
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    HeadTop:
      points: 1
      required: true
      defaultMarkings: [ GoblinEarsBasic ]
    Snout:
      points: 2
      required: false
#      defaultMarkings: [ GoblinNoseBasic ]
    Chest:
      points: 1
      required: false
    RightLeg:
      points: 2
      required: false
    RightFoot:
      points: 2
      required: false
    LeftLeg:
      points: 2
      required: false
    LeftFoot:
      points: 2
      required: false
    RightArm:
      points: 2
      required: false
    RightHand:
      points: 2
      required: false
    LeftArm:
      points: 2
      required: false
    LeftHand:
      points: 2
      required: false

- type: humanoidBaseSprite
  id: MobGoblinEyes
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobGoblinHead
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobGoblinHeadMale
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobGoblinHeadFemale
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobGoblinTorso
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobGoblinTorsoMale
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobGoblinTorsoFemale
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobGoblinLLeg
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobGoblinLArm
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobGoblinLHand
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobGoblinLFoot
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobGoblinRLeg
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobGoblinRArm
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobGoblinRHand
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobGoblinRFoot
  baseSprite:
    sprite: _NF/Mobs/Species/Goblin/parts.rsi
    state: r_foot
