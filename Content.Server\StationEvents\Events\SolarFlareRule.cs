using Content.Server.GameTicking.Rules.Components;
using Content.Server.Radio;
using Robust.Shared.Random;
using Content.Server.Light.EntitySystems;
using Content.Server.Light.Components;
using Content.Server.StationEvents.Components;
using Content.Shared.Radio.Components;
using Content.Shared.Doors.Components;
using Content.Shared.Doors.Systems;
using Content.Shared.GameTicking.Components;

namespace Content.Server.StationEvents.Events;

public sealed class SolarFlareRule : StationEventSystem<SolarFlareRuleComponent>
{
    [Dependency] private readonly PoweredLightSystem _poweredLight = default!;
    [Dependency] private readonly SharedDoorSystem _door = default!;

    private float _effectTimer = 0;
    private const float EffectInterval = 1f;

    public override void Initialize()
    {
        base.Initialize();
        SubscribeLocalEvent<RadioReceiveAttemptEvent>(OnRadioReceiveAttempt);
    }

    protected override void Started(EntityUid uid, SolarFlareRuleComponent comp, GameRuleComponent gameRule, GameRuleStartedEvent args)
    {
        base.Started(uid, comp, gameRule, args);

        for (var i = 0; i < comp.ExtraCount; i++)
        {
            var channel = RobustRandom.Pick(comp.ExtraChannels);
            comp.AffectedChannels.Add(channel);
        }
    }

    protected override void ActiveTick(EntityUid uid, SolarFlareRuleComponent component, GameRuleComponent gameRule, float frameTime)
    {
        base.ActiveTick(uid, component, gameRule, frameTime);

        _effectTimer += frameTime;
        if (_effectTimer < EffectInterval)
            return;
        _effectTimer = 0f;

        var lightQuery = EntityQueryEnumerator<PoweredLightComponent>();
        while (lightQuery.MoveNext(out var lightEnt, out var light))
        {
            if (!RobustRandom.Prob(component.LightBreakChancePerSecond))
                continue;

            _poweredLight.TryDestroyBulb(lightEnt, light);
        }

        var airlockQuery = EntityQueryEnumerator<AirlockComponent, DoorComponent>();
        while (airlockQuery.MoveNext(out var airlockEnt, out var airlock, out var door))
        {
            if (!airlock.AutoClose || !RobustRandom.Prob(component.DoorToggleChancePerSecond))
                continue;

            _door.TryToggleDoor(airlockEnt, door);
        }
    }

    private void OnRadioReceiveAttempt(ref RadioReceiveAttemptEvent args)
    {
        var query = EntityQueryEnumerator<SolarFlareRuleComponent, GameRuleComponent>();
        while (query.MoveNext(out var uid, out var flare, out var gameRule))
        {
            if (!GameTicker.IsGameRuleActive(uid, gameRule))
                continue;

            if (!flare.AffectedChannels.Contains(args.Channel.ID))
                continue;

            if (!flare.OnlyJamHeadsets || (HasComp<HeadsetComponent>(args.RadioReceiver) || HasComp<HeadsetComponent>(args.RadioSource)))
                args.Cancelled = true;
        }
    }
}
