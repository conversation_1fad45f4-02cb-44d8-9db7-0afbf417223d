- type: species
  id: Moth
  name: species-name-moth
  roundStart: true
  prototype: MobMoth
  sprites: MobMothSprites
  defaultSkinTone: "#ffda93"
  markingLimits: MobMothMarkingLimits
  dollPrototype: MobMothDummy
  skinColoration: Hu<PERSON>
  maleFirstNames: names_moth_first_male
  femaleFirstNames: names_moth_first_female
  lastNames: names_moth_last

- type: speciesBaseSprites
  id: MobMothSprites
  sprites:
    Head: MobMothHead
    Face: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Chest: MobMothTorso
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Eyes: MobMothEyes
    LArm: MobMothLArm
    RArm: MobMothRArm
    LHand: MobMothLHand
    RHand: MobMothRHand
    LLeg: MobMothLLeg
    RLeg: MobMothRLeg
    LFoot: MobMothLFoot
    RFoot: MobMothRFoot

- type: humanoidBaseSprite
  id: MobMothEyes
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: eyes

- type: markingPoints
  id: MobMothMarkingLimits
  onlyWhitelisted: true
  points:
    Hair:
      points: 0
      required: false
    FacialHair:
      points: 0
      required: false
      # PIRATE START
    Face:
      points: 2
      required: false
      # PIRATE END
    Tail:
      points: 1
      required: true
      defaultMarkings: [ MothWingsDefault ]
    Snout:
      points: 1
      required: true
      defaultMarkings: [ MothRegularSnout ]
    HeadTop:
      points: 6
      required: true
      defaultMarkings: [ MothAntennasDefault ]
    HeadSide:
      points: 6
      required: false
    Head:
      points: 6
      required: false
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobMothHead
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobMothHeadMale
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobMothHeadFemale
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobMothTorso
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobMothTorsoMale
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobMothTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobMothLLeg
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobMothLHand
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobMothLArm
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobMothLFoot
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobMothRLeg
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobMothRHand
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobMothRArm
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobMothRFoot
  baseSprite:
    sprite: Mobs/Species/Moth/parts.rsi
    state: r_foot
