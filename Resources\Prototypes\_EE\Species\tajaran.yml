- type: species
  id: Tajaran
  name: species-name-tajaran
  roundStart: true
  prototype: MobTajaran
  sprites: MobTajaranSprites
  markingLimits: MobTajaranMarkingLimits
  dollPrototype: MobTajaranDummy
  skinColoration: AnimalFur
  minHeight: 0.65
  defaultHeight: 0.8
  maxHeight: 1.1
  minWidth: 0.6
  defaultWidth: 0.8
  maxWidth: 1.15
  maleFirstNames: names_tajaran_first
  femaleFirstNames: names_tajaran_first
  lastNames: names_tajaran_last


- type: markingPoints
  id: MobTajaranMarkingLimits
  points:
    # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
    # PIRATE END
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ TajaranTailRetro ]
    HeadTop:
      points: 1
      required: true
      defaultMarkings: [ TajaranEarsBasic ]
    Chest:
      points: 1
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: speciesBaseSprites
  id: MobTajaranSprites
  sprites:
    Head: MobTajaranHead
    Face: MobHumanoidAnyMarking
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Chest: MobTajaranTorso
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Eyes: MobTajaranEyes
    LArm: MobTajaranLArm
    RArm: MobTajaranRArm
    LHand: MobTajaranLHand
    RHand: MobTajaranRHand
    LLeg: MobTajaranLLeg
    RLeg: MobTajaranRLeg
    Tail: MobHumanoidAnyMarking
    LFoot: MobTajaranLFoot
    RFoot: MobTajaranRFoot

- type: humanoidBaseSprite
  id: MobTajaranEyes
  baseSprite:
    sprite: Mobs/Customization/eyes.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobTajaranHead
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobTajaranHeadMale
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobTajaranHeadFemale
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobTajaranTorso
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobTajaranTorsoMale
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobTajaranTorsoFemale
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobTajaranLLeg
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobTajaranLArm
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobTajaranLHand
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobTajaranLFoot
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobTajaranRLeg
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobTajaranRArm
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobTajaranRHand
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobTajaranRFoot
  baseSprite:
    sprite: _EE/Mobs/Species/Tajaran/parts.rsi
    state: r_foot
