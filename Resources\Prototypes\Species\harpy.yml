- type: species
  id: Harpy
  name: species-name-harpy
  roundStart: true
  prototype: MobHarpy
  sprites: MobHarpySprites
  defaultSkinTone: "#c0967f"
  markingLimits: MobHarpyMarkingLimits
  dollPrototype: MobHarpyDummy
  skinColoration: HumanToned
  minHeight: 0.6
  defaultHeight: 0.8
  maxHeight: 1.1
  minWidth: 0.55
  defaultWidth: 0.8
  maxWidth: 1.15

- type: speciesBaseSprites
  id: MobHarpySprites
  sprites:
    Head: MobHarpyHead
    Face: MobHumanoidAnyMarking
    Hair: MobHumanoidAnyMarking
    FacialHair: MobHumanoidAnyMarking
    Snout: MobHumanoidAnyMarking
    Chest: MobHarpyTorso
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Tail: MobHumanoidAnyMarking
    Eyes: MobHumanoidEyes
    LArm: MobHarpyLArm
    RArm: MobHarpyRArm
    LHand: MobHarpyLHand
    RHand: MobHarpyRHand
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    LLeg: MobHarpyLLeg
    RLeg: MobHarpyRLeg
    LFoot: MobHarpyLFoot
    RFoot: MobHarpyRFoot


- type: markingPoints
  id: MobHarpyMarkingLimits
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
  # PIRATE END
    Hair:
      points: 1
      required: false
    FacialHair:
      points: 1
      required: false
    Tail:
      points: 1
      required: true
      defaultMarkings: [ HarpyTailPhoenix ]
    HeadTop:
      points: 2
      required: true
      defaultMarkings: [ HarpyEarsDefault ]
    HeadSide:
      points: 6
      required: false
    Chest:
      points: 6
      required: true
      defaultMarkings: [ HarpyChestDefault ]
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 1
      required: false
      defaultMarkings: [ HarpyWingDefaultHuescale ]
    RightHand:
      points: 2
      required: false
    LeftArm:
      points: 2
      required: false
    LeftHand:
      points: 2
      required: false

- type: humanoidBaseSprite
  id: MobHarpyHead
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobHarpyHeadMale
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobHarpyHeadFemale
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobHarpyTorso
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobHarpyTorsoMale
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobHarpyTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobHarpyLLeg
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobHarpyLHand
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobHarpyLArm
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobHarpyLFoot
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobHarpyRLeg
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobHarpyRHand
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobHarpyRArm
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobHarpyRFoot
  baseSprite:
    sprite: Mobs/Species/Harpy/parts.rsi
    state: r_foot
