- type: species
  id: Shadowkin
  name: species-name-shadowkin
  roundStart: true
  prototype: MobShadowkin
  sprites: MobShadowkinSprites
  defaultSkinTone: "#FFFFFF"
  markingLimits: MobShadowkinMarkingLimits
  dollPrototype: MobShadowkinDummy
  skinColoration: Hues
  naming: First
  maleFirstNames: names_shadowkin
  femaleFirstNames: names_shadowkin
  minAge: 18
  maxAge: 250
  youngAge: 80
  oldAge: 175
  sexes:
    - Male
    - Female
  minHeight: 0.65
  defaultHeight: 0.85
  maxHeight: 1.15
  minWidth: 0.6
  defaultWidth: 0.85
  maxWidth: 1.2

- type: speciesBaseSprites
  id: MobShadowkinSprites
  sprites:
    Head: MobShadowkinHead
    Snout: MobShadowkinAnyMarkingFollowSkin
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    HeadTop: MobShadowkinAnyMarking
    HeadSide: MobShadowkinAnyMarkingFollowSkin
    Tail: MobShadowkinAnyMarkingFollowSkin
    Chest: MobShadowkinTorso
    Eyes: MobShadowkinEyes
    LArm: MobShadowkinLArm
    RArm: MobShadowkinRArm
    LHand: MobShadowkinLHand
    RHand: MobShadowkinRHand
    LLeg: MobShadowkinLLeg
    RLeg: MobShadowkinRLeg
    LFoot: MobShadowkinLFoot
    RFoot: MobShadowkinRFoot

- type: markingPoints
  id: MobShadowkinMarkingLimits
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
  # PIRATE END
    Tail:
      points: 1
      required: true
      defaultMarkings: [TailShadowkin]
    HeadTop:
      points: 1
      required: true
      defaultMarkings: [EarsShadowkin]
    Chest:
      points: 6
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobShadowkinAnyMarkingFollowSkin
  markingsMatchSkin: true

- type: humanoidBaseSprite
  id: MobShadowkinAnyMarking

- type: humanoidBaseSprite
  id: MobShadowkinHead
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobShadowkinHeadMale
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobShadowkinHeadFemale
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobShadowkinTorso
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobShadowkinTorsoMale
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobShadowkinTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobShadowkinLLeg
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobShadowkinLHand
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobShadowkinEyes
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobShadowkinLArm
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobShadowkinLFoot
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobShadowkinRLeg
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobShadowkinRHand
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobShadowkinRArm
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobShadowkinRFoot
  baseSprite:
    sprite: Mobs/Species/Shadowkin/parts.rsi
    state: r_foot