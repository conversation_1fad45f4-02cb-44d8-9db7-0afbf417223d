- type: species
  id: Thaven
  name: species-name-thaven
  roundStart: true
  prototype: MobThaven
  sprites: MobThavenSprites
  defaultSkinTone: "#ffffff"
  markingLimits: MobThavenMarkingLimits
  dollPrototype: MobThavenDummy
  skinColoration: Hu<PERSON>
  maleFirstNames: names_thaven
  femaleFirstNames: names_thaven
  naming: First

- type: speciesBaseSprites
  id: MobThavenSprites
  sprites:
    Hair: MobHumanoidAnyMarking
    Eyes: MobThavenEyes
    Head: MobThavenHead
    HeadTop: MobHumanoidAnyMarking
    HeadSide: MobHumanoidAnyMarking
    Chest: MobThavenTorso
    LArm: MobThavenLArm
    RArm: MobThavenRArm
    LHand: MobThavenLHand
    RHand: MobThavenRHand
    LLeg: MobThavenLLeg
    RLeg: MobThavenRLeg
    LFoot: MobThavenLFoot
    RFoot: MobThavenRFoot

- type: markingPoints
  id: MobThavenMarkingLimits
  points:
  # PIRATE START
    Head:
      points: 2
      required: false
    Face:
      points: 3
      required: false
  # PIRATE END
    Hair:
      points: 1
      required: false
    Snout:
      points: 1
      required: false
    HeadTop:
      points: 1
      required: false
    HeadSide:
      points: 4
      required: false
      defaultMarkings: [ ThavenEars1 ]
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 6
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobThavenEyes
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: eyes

- type: humanoidBaseSprite
  id: MobThavenHead
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: head

- type: humanoidBaseSprite
  id: MobThavenHeadMale
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: head

- type: humanoidBaseSprite
  id: MobThavenHeadFemale
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: head

- type: humanoidBaseSprite
  id: MobThavenTorso
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobThavenTorsoMale
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobThavenTorsoFemale
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: torso_f


- type: humanoidBaseSprite
  id: MobThavenLLeg
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobThavenLHand
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobThavenLArm
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobThavenLFoot
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobThavenRLeg
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobThavenRHand
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobThavenRArm
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobThavenRFoot
  baseSprite:
    sprite: _Impstation/Mobs/Species/Thaven/parts.rsi
    state: r_foot
