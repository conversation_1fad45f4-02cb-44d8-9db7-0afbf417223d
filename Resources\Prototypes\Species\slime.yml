- type: species
  id: SlimePerson
  name: species-name-slime
  roundStart: true
  prototype: MobSlimePerson
  sprites: MobSlimeSprites
  defaultSkinTone: "#b8b8b8"
  markingLimits: MobSlimeMarkingLimits
  dollPrototype: MobSlimePersonDummy
  skinColoration: Hues

- type: speciesBaseSprites
  id: MobSlimeSprites
  sprites:
    Face: MobHumanoidAnyMarking
    Head: MobSlimeHead
    HeadSide: MobHumanoidAnyMarking
    Hair: MobSlimeMarkingFollowSkin
    FacialHair: MobSlimeMarkingFollowSkin
    Underwear: MobHumanoidAnyMarking
    Undershirt: MobHumanoidAnyMarking
    Chest: MobSlimeTorso
    Eyes: MobHumanoidEyes
    LArm: MobSlimeLArm
    RArm: MobSlimeRArm
    LHand: MobSlimeLHand
    RHand: MobSlimeRHand
    LLeg: MobSlimeLLeg
    RLeg: MobSlimeRLeg
    LFoot: MobSlimeLFoot
    RFoot: MobSlimeRFoot

- type: markingPoints
  id: MobSlimeMarkingLimits
  points:
    Hair:
      points: 1
      required: false
    HeadSide:
      points: 6
      required: false
    HeadTop: # PIRATE - Added HeadTop support for slime ears
      points: 1 # PIRATE
      required: false # PIRATE
    FacialHair:
      points: 1
      required: false
    Tail: # PIRATE - Added Tail support for slime tails
      points: 1 # PIRATE
      required: false # PIRATE
    Chest:
      points: 6
      required: false
    Underwear:
      points: 1
      required: false
    Undershirt:
      points: 1
      required: false
    RightLeg:
      points: 2
      required: false
    RightFoot:
      points: 6
      required: false
    LeftLeg:
      points: 6
      required: false
    LeftFoot:
      points: 6
      required: false
    RightArm:
      points: 6
      required: false
    RightHand:
      points: 6
      required: false
    LeftArm:
      points: 6
      required: false
    LeftHand:
      points: 6
      required: false

- type: humanoidBaseSprite
  id: MobSlimeMarkingFollowSkin
  markingsMatchSkin: true
  layerAlpha: 0.75

- type: humanoidBaseSprite
  id: MobSlimeHead
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobSlimeHeadMale
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: head_m

- type: humanoidBaseSprite
  id: MobSlimeHeadFemale
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: head_f

- type: humanoidBaseSprite
  id: MobSlimeTorso
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobSlimeTorsoMale
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: torso_m

- type: humanoidBaseSprite
  id: MobSlimeTorsoFemale
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: torso_f

- type: humanoidBaseSprite
  id: MobSlimeLLeg
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: l_leg

- type: humanoidBaseSprite
  id: MobSlimeLArm
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: l_arm

- type: humanoidBaseSprite
  id: MobSlimeLHand
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: l_hand

- type: humanoidBaseSprite
  id: MobSlimeLFoot
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: l_foot

- type: humanoidBaseSprite
  id: MobSlimeRLeg
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: r_leg

- type: humanoidBaseSprite
  id: MobSlimeRArm
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: r_arm

- type: humanoidBaseSprite
  id: MobSlimeRHand
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: r_hand

- type: humanoidBaseSprite
  id: MobSlimeRFoot
  baseSprite:
    sprite: Mobs/Species/Slime/parts.rsi
    state: r_foot
