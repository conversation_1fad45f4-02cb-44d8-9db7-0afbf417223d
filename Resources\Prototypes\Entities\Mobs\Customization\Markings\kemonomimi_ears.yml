- type: marking
  id: BullishHorns
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Human, IPC, Oni]
  coloring:
    default:
      type:
        !type:CategoryColoring
          category: Hair
      fallbackTypes:
        - !type:SkinColoring
  sprites:
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: bullishhorns

- type: marking
  id: BunnyEars
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Human, IPC, SlimePerson] # PIRATE - Added SlimePerson support
  coloring:
    default:
      type:
        !type:CategoryColoring
          category: Hair
      fallbackTypes:
        - !type:SkinColoring
    layers:
      bunnyearstone2:
        type:
          !type:SimpleColoring
            color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: bunnyearstone1
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: bunnyearstone2

- type: marking
  id: BunnyEarsAlt
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Human, IPC]
  coloring:
    default:
      type:
        !type:CategoryColoring
          category: Hair
      fallbackTypes:
        - !type:SkinColoring
  sprites:
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: bunnyearsalt

- type: marking
  id: FoxEars
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Human, IPC, SlimePerson] # PIRATE - Added SlimePerson support
  coloring:
    default:
      type:
        !type:CategoryColoring
          category: Hair
      fallbackTypes:
        - !type:SkinColoring
  sprites:
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: foxears

- type: marking
  id: GoatHorns
  bodyPart: HeadTop
  markingCategory: HeadTop
  speciesRestriction: [Human, IPC]
  coloring:
    default:
      type:
        !type:CategoryColoring
          category: Hair
      fallbackTypes:
        - !type:SkinColoring
    layers:
      goathornstone22:
        type:
          !type:SimpleColoring
            color: "#FFFFFF"
  sprites:
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: goathornstone1
  - sprite: Mobs/Customization/kemonomimi-ears.rsi
    state: goathornstone2
