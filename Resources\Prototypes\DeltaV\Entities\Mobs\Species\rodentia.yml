- type: entity
  save: false
  name: Urist McRat
  parent: BaseMobSpeciesOrganic
  id: BaseMobRodentia
  abstract: true
  components:
  - type: HumanoidAppearance
    species: Rodentia
  - type: Hunger
    baseDecayRate: 0.0467 # 33% faster than usual
  - type: Carriable # Carrying system from nyanotrasen.
  - type: Inventory
    templateId: rodentia #PIRATE
    speciesId: rodentia
  - type: Icon
    sprite: DeltaV/Mobs/Species/Rodentia/parts.rsi
    state: full
  - type: Body
    prototype: Rodentia
  - type: Speech
    speechVerb: Rodentia
    speechSounds: Squeak
    allowedEmotes: ['Squeak']
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.28
        density: 140
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: ScaleData # Goobstation
    scale: 0.8, 0.8
  - type: Sprite
    scale: 0.8, 0.8
    layers:
      - map: [ "enum.HumanoidVisualLayers.Chest" ]
      - map: [ "enum.HumanoidVisualLayers.Head" ]
      - map: [ "enum.HumanoidVisualLayers.Snout" ]
      - map: [ "enum.HumanoidVisualLayers.Eyes" ]
      - map: [ "enum.HumanoidVisualLayers.RArm" ]
      - map: [ "enum.HumanoidVisualLayers.LArm" ]
      - map: [ "enum.HumanoidVisualLayers.RLeg" ]
      - map: [ "enum.HumanoidVisualLayers.LLeg" ]
      - map: [ "jumpsuit" ]
      - map: [ "enum.HumanoidVisualLayers.LHand" ]
      - map: [ "enum.HumanoidVisualLayers.RHand" ]
      - map: [ "enum.HumanoidVisualLayers.LFoot" ]
      - map: [ "enum.HumanoidVisualLayers.RFoot" ]
      - map: [ "enum.HumanoidVisualLayers.Handcuffs" ]
        color: "#ffffff"
        sprite: Objects/Misc/handcuffs.rsi
        state: body-overlay-2
        visible: false
      - map: [ "id" ]
      - map: [ "gloves" ]
      - map: [ "shoes" ]
      - map: [ "ears" ]
      - map: [ "outerClothing" ]
      - map: [ "eyes" ]
      - map: [ "belt" ]
      - map: [ "neck" ]
      - map: [ "back" ]
      - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
      - map: [ "enum.HumanoidVisualLayers.Hair" ]
      - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
      - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
      - map: [ "enum.HumanoidVisualLayers.Tail" ]
      - map: [ "mask" ]
      - map: [ "head" ]
      - map: [ "pocket1" ]
      - map: [ "pocket2" ]
      - map: [ "clownedon" ] # Dynamically generated
        sprite: "DeltaV/Effects/creampie.rsi"
        state: "creampie_rodentia"
        visible: false
  - type: MeleeWeapon
    soundHit:
      path: /Audio/Weapons/pierce.ogg
    angle: 30
    animation: WeaponArcClaw
    damage:
      types:
        Blunt: 2
        Slash: 3
  - type: Damageable
    damageModifierSet: Rodentia
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 5
  - type: TypingIndicator
    proto: rodentia
  - type: Vocal
    sounds:
      Male: MaleRodentia
      Female: FemaleRodentia
      Unsexed: MaleRodentia
  - type: Rummager
  - type: AlwaysTriggerMousetrap
  - type: PseudoItem
    storedOffset: -10,0
    shape:
      - 0, 0, 3, 2
      - 0, 2, 5, 4
  - type: MouthStorage
    mouthProto: CheekStorage
    openStorageAction: ActionOpenMouthStorage
    spitDamageThreshold: 3
  - type: CrawlUnderObjects
    actionProto: ActionToggleSneakMode

- type: entity
  save: false
  name: Rodentia Dummy
  parent: MobHumanDummy
  id: MobRodentiaDummy
  description: A dummy rodentia meant to be used in character setup.
  components:
  - type: HumanoidAppearance
    species: Rodentia
  - type: Inventory
    templateId: rodentia #PIRATE
    speciesId: rodentia
