- type: gameMap
  id: Cyberiad
  mapName: 'Кіберіада'
  mapPath: /Maps/cyberiad.yml
  minPlayers: 30
  maxPlayers: 85
  stations:
    Cyberiad:
      stationProto: StandardNanotrasenStationCargoOnly
      components:
        - type: StationNameSetup
          mapNameTemplate: 'NSS Кіберіада {1}'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_box.yml
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            # AdministrativeAssistant: [ 1, 1 ] # EE: Disabled
            # Magistrate: [ 1, 1 ] # EE: Disabled
            Bartender: [ 2, 2 ]
            Botanist: [ 3, 3 ]
            Chef: [ 2, 2 ]
            Janitor: [ 2, 2 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 3, 3 ]
            StationEngineer: [ 5, 5 ]
            TechnicalAssistant: [ 4, 4 ]
            SeniorEngineer: [ 1, 1 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 4, 4 ]
            Paramedic: [ 1, 1 ]
            MedicalIntern: [ 4, 4 ]
            Psychologist: [ 1, 1 ]
            SeniorPhysician: [ 1, 1 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 5, 5 ]
            Roboticist: [ 1, 2 ]
            ResearchAssistant: [ 4, 4 ]
            ForensicMantis: [ 1, 1 ]
            SeniorResearcher: [ 1, 1 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 5, 5 ]
            Brigmedic: [ 1, 1 ]
            Prisoner: [ 1, 3 ]
            # PrisonGuard: [ 1, 2 ] # EE: Disabled
            Detective: [ 1, 1 ]
            SecurityCadet: [ 4, 4 ]
            Lawyer: [ 2, 2 ]
            SeniorOfficer: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 3, 3 ]
            CargoTechnician: [ 3, 5 ]
            MailCarrier: [ 1, 2 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 2 ]
            Reporter: [ 1, 1 ]
            #silicon
            StationAi: [ 1, 1 ]
            Borg: [ 2, 2 ]
            MedicalBorg: [ 2, 2 ]
        # Goobstation blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
        # Goobstation blob-config-end
